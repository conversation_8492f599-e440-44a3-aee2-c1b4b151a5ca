"use client";

import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";

const skills = ["JavaScript", "TypeScript", "Node.js", "Express.js", "MongoDB", "AWS", "Docker", "Redis", "REST APIs", "Angular", "SASS", "Git", "Microservices", "NATS", "MQTT"];

// Duplicate skills for seamless infinite scroll
const duplicatedSkills = [...skills, ...skills];

export default function SkillsSection() {
  const [isPaused, setIsPaused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollingRef = useRef<HTMLDivElement>(null);
  const [animationDistance, setAnimationDistance] = useState(-1920);

  useEffect(() => {
    const calculateAnimationDistance = () => {
      if (scrollingRef.current) {
        // Calculate the width of one set of skills (half of the duplicated array)
        const singleSetWidth = scrollingRef.current.scrollWidth / 2;
        setAnimationDistance(-singleSetWidth);
      }
    };

    // Calculate on mount and resize
    calculateAnimationDistance();
    window.addEventListener("resize", calculateAnimationDistance);

    return () => window.removeEventListener("resize", calculateAnimationDistance);
  }, []);

  return (
    <section className="py-20 px-4">
      <div className="max-w-6xl mx-auto">
        <div className="mb-16"></div>

        <div className="relative overflow-hidden" ref={containerRef}>
          {/* Gradient overlays for fade effect */}
          <div className="absolute left-0 top-0 w-32 h-full bg-gradient-to-r from-gray-950 to-transparent z-10 pointer-events-none" />
          <div className="absolute right-0 top-0 w-32 h-full bg-gradient-to-l from-gray-950 to-transparent z-10 pointer-events-none" />

          {/* Scrolling container */}
          <div className="overflow-hidden" onMouseEnter={() => setIsPaused(true)} onMouseLeave={() => setIsPaused(false)}>
            <motion.div
              ref={scrollingRef}
              className="flex gap-6 w-fit"
              animate={{
                x: isPaused ? undefined : [0, animationDistance],
              }}
              transition={{
                x: {
                  repeat: Infinity,
                  repeatType: "loop",
                  duration: 42,
                  ease: "linear",
                },
              }}
            >
              {duplicatedSkills.map((skill, index) => (
                <SkillCard key={`${skill}-${index}`} skill={skill} />
              ))}
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}

interface SkillCardProps {
  skill: string;
}

function SkillCard({ skill }: SkillCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className="relative group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ scale: 1.05 }}
      transition={{ duration: 0.2 }}
    >
      {/* Gradient border on hover */}
      <div
        className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${isHovered ? "opacity-100" : "opacity-0"}`}
        style={{
          background: "linear-gradient(45deg, rgba(20, 184, 166, 0.8), rgba(59, 130, 246, 0.6), rgba(168, 85, 247, 0.6), rgba(20, 184, 166, 0.8))",
          padding: "2px",
        }}
      >
        <div className="w-full h-full bg-gray-900/90 rounded-xl" />
      </div>

      {/* Glass effect card */}
      <div className="relative px-6 py-4 min-w-[140px] text-center">
        {/* Glass background */}
        <div className="absolute inset-0 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10" />

        {/* Content */}
        <div className="relative z-10">
          <span className="text-sm font-medium text-gray-200 whitespace-nowrap">{skill}</span>
        </div>

        {/* Hover glow effect */}
        <div
          className={`absolute inset-0 rounded-xl transition-opacity duration-300 ${isHovered ? "opacity-20" : "opacity-0"}`}
          style={{
            background: "radial-gradient(circle at center, rgba(20, 184, 166, 0.3) 0%, transparent 70%)",
          }}
        />
      </div>
    </motion.div>
  );
}
