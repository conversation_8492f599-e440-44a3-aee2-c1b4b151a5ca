import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NODE_ENV === "production" ? "https://benbasiltomy.com" : "http://localhost:3000"),
  title: "[<PERSON>] - Software Engineer",
  description:
    "Welcome to my portfolio! I am a passionate Software Engineer specializing in creating complete web solutions, from beautiful user interfaces to robust server architectures.",
  keywords: [
    "Full-Stack Developer",
    "Software Engineer",
    "Frontend Development",
    "Backend Development",
    "React",
    "Next.js",
    "Node.js",
    "TypeScript",
    "Database Design",
    "API Development",
    "Cloud Solutions",
    "DevOps",
    "System Architecture",
    "Web Development",
    "[<PERSON>]",
  ],
  authors: [{ name: "[<PERSON>]" }],
  creator: "[<PERSON>]",
  openGraph: {
    title: "[<PERSON>] - Software Engineer Portfolio",
    description: "Passionate software engineer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",
    url: "https://benbasiltomy.com",
    siteName: "[Ben Basil Tomy] - Portfolio",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "[Ben Basil Tomy] - Software Engineer",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "[Ben Basil Tomy] - Software Engineer",
    description: "Passionate full-stack developer creating complete web solutions. Explore my projects and technical expertise across the entire development stack.",
    creator: "@yourusername",
    images: ["/og-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>{children}</body>
    </html>
  );
}
