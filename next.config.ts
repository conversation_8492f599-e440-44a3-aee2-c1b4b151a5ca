import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    // Disable ESLint during builds
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Optionally ignore TypeScript errors during builds too
    ignoreBuildErrors: false,
  },
  // For static export (uncomment if you want static site)
  // output: "export",
  // trailingSlash: true,
  // Optimize for production deployment
  output: "standalone",
  // Enable compression
  compress: true,
  // Optimize images
  images: {
    formats: ["image/webp", "image/avif"],
  },
};

export default nextConfig;
