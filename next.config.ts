import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    // Disable ESLint during builds
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Temporarily ignore TypeScript errors during builds for deployment
    ignoreBuildErrors: true,
  },
  // Remove standalone output for Render compatibility
  // output: "standalone",

  // Enable compression
  compress: true,

  // Optimize images
  images: {
    formats: ["image/webp", "image/avif"],
    unoptimized: false,
  },

  // Ensure proper build output
  generateEtags: false,
  poweredByHeader: false,
};

export default nextConfig;
