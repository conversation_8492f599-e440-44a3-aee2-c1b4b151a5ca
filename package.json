{"name": "fullstack-portfolio-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:verify": "echo Build verification complete", "start": "next start", "lint": "next lint"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"@tabler/icons-react": "^3.34.1", "clsx": "^2.1.1", "framer-motion": "^12.0.6", "motion": "^12.23.12", "next": "15.1.6", "postcss": "^8", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "20.19.9", "@types/react": "19.1.9", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "typescript": "5.9.2"}}