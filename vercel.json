{"buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "framework": "nextjs", "regions": ["iad1"], "functions": {"app/**/*.{js,ts,tsx}": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/(.*)\\.(jpg|jpeg|png|gif|ico|svg|webp|avif)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}